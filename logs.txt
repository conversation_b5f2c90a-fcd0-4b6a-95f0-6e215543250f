2025-07-28 06:56:24 [INFO] ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 06:56:24 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 06:56:24 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 06:56:24 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 06:56:24 [INFO] 🔍 智能日志监控已启动
2025-07-28 06:56:24 [ERROR] 无法访问日志文件: [Errno 13] Permission denied: 'D:\\AIGC\\AI_Video\\index-tts\\logs.txt'
2025-07-28 06:56:24 [INFO] 🔍 错误分析: 🔒 文件权限不足
2025-07-28 06:56:24 [INFO] 💡 建议解决方案:
2025-07-28 06:56:24 [INFO]    1. 以管理员身份运行程序
2025-07-28 06:56:24 [INFO]    2. 检查文件和目录权限
2025-07-28 06:56:24 [INFO]    3. 关闭占用文件的其他程序
2025-07-28 06:56:24 [INFO]    4. 更改文件所有者权限
2025-07-28 06:56:24 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 06:56:24 [INFO] 💡 解决方案:
2025-07-28 06:56:24 [INFO] 1. 关闭所有打开logs.txt的程序
2025-07-28 06:56:24 [INFO] 2. 以管理员身份运行程序
2025-07-28 06:56:24 [INFO] 3. 检查文件权限设置
2025-07-28 06:56:24 [WARNING] 程序将继续运行，日志仅输出到控制台
⚠️ 日志文件初始化失败: [Errno 13] Permission denied: 'D:\\AIGC\\AI_Video\\index-tts\\logs.txt'
💡 解决方案:
1. 检查文件权限
2. 确保目录存在
3. 以管理员身份运行
2025-07-28 06:56:26,040 [INFO] ============================================================
2025-07-28 06:56:26,040 [INFO] 📋 智能错误分析报告
2025-07-28 06:56:26,040 [INFO] ============================================================
2025-07-28 06:56:26,040 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 06:56:26,040 [INFO] ⚠️  严重程度: 中
2025-07-28 06:56:26,040 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 06:56:26,040 [INFO] 💡 解决方案:
2025-07-28 06:56:26,040 [INFO]    1. 以管理员身份运行程序
2025-07-28 06:56:26,040 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 06:56:26,040 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 06:56:26,040 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 06:56:26,040 [INFO] ============================================================
2025-07-28 06:56:26,040 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 06:56:26,054 [INFO] === IndexTTS 系统检查 ===
2025-07-28 06:56:26,054 [INFO] Python版本: 3.10.16
2025-07-28 06:56:26,054 [INFO] PyTorch版本: 2.5.1+cu121
2025-07-28 06:56:26,073 [INFO] ✅ CUDA可用: 是
2025-07-28 06:56:26,073 [INFO] CUDA版本: 12.1
2025-07-28 06:56:26,073 [INFO] GPU数量: 1
2025-07-28 06:56:26,076 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 06:56:26,076 [INFO] 磁盘空间: 总计 631 GB, 已用 317 GB, 可用 313 GB
2025-07-28 06:56:26,076 [INFO] === 系统检查完成 ===
2025-07-28 06:56:26,078 [INFO] 🔄 开始: 初始化IndexTTS模型
Detected CUDA files, patching ldflags
Emitting ninja build file D:\AIGC\AI_Video\index-tts\indextts\BigVGAN\alias_free_activation\cuda\build\build.ninja...
INFO: Could not find files for the given pattern(s).
>> Failed to load custom CUDA kernel for BigVGAN. Falling back to torch. Command '['where', 'cl']' returned non-zero exit status 1.
 Reinstall with `pip install -e . --no-deps --no-build-isolation` to prebuild `anti_alias_activation_cuda` kernel.
See more details: https://github.com/index-tts/index-tts/issues/164#issuecomment-2903453206
>> 检测到CUDA设备: cuda:0
>> GPU型号: NVIDIA GeForce RTX 2060
>> 显存大小: 12.0 GB
>> 计算能力: 7.5
>> ✅ 检测到RTX 20系显卡，完全支持
>> ✅ 计算能力良好，推荐配置
>> GPT weights restored from: checkpoints\gpt.pth
>> DeepSpeed加载失败，回退到标准推理: No module named 'deepspeed'
See more details https://www.deepspeed.ai/tutorials/advanced-install/
2025-07-28 06:56:36,067 [INFO] ============================================================
2025-07-28 06:56:36,067 [INFO] 📋 智能错误分析报告
2025-07-28 06:56:36,067 [INFO] ============================================================
2025-07-28 06:56:36,067 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 06:56:36,068 [INFO] ⚠️  严重程度: 低
2025-07-28 06:56:36,068 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 06:56:36,068 [INFO] 💡 解决方案:
2025-07-28 06:56:36,068 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 06:56:36,068 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 06:56:36,068 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 06:56:36,068 [INFO] ============================================================
2025-07-28 06:56:36,068 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 06:56:36,827 WETEXT INFO found existing fst: D:\AIGC\AI_Video\index-tts\indextts\utils\tagger_cache\zh_tn_tagger.fst
Removing weight norm...
>> bigvgan weights restored from: checkpoints\bigvgan_generator.pth
2025-07-28 06:56:36,827 [INFO] found existing fst: D:\AIGC\AI_Video\index-tts\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 06:56:36,827 WETEXT INFO                     D:\AIGC\AI_Video\index-tts\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 06:56:36,827 [INFO]                     D:\AIGC\AI_Video\index-tts\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 06:56:36,827 WETEXT INFO skip building fst for zh_normalizer ...
2025-07-28 06:56:36,827 [INFO] skip building fst for zh_normalizer ...
2025-07-28 06:56:37,240 WETEXT INFO found existing fst: D:\AIGC\AI_Video\index-tts\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 06:56:37,240 [INFO] found existing fst: D:\AIGC\AI_Video\index-tts\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 06:56:37,240 WETEXT INFO                     D:\AIGC\AI_Video\index-tts\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 06:56:37,240 [INFO]                     D:\AIGC\AI_Video\index-tts\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 06:56:37,240 WETEXT INFO skip building fst for en_normalizer ...
2025-07-28 06:56:37,240 [INFO] skip building fst for en_normalizer ...
>> TextNormalizer loaded
>> bpe model loaded from: checkpoints\bpe.model
2025-07-28 06:56:37,979 [INFO] ✅ IndexTTS模型初始化成功
2025-07-28 06:56:38,255 [INFO] 🔍 找到可用端口: 7866
2025-07-28 06:56:38,255 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 06:56:38,255 [INFO] 🌐 访问地址: http://127.0.0.1:7866
* Running on local URL:  http://127.0.0.1:7866
2025-07-28 06:56:38,352 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 06:56:38,372 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
* To create a public link, set `share=True` in `launch()`.
2025-07-28 06:56:38,953 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-28 22:16:08 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 22:16:08 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 22:16:08 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 22:16:08 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 22:16:08 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 22:16:08 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 22:16:08 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 22:16:08 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 22:16:08 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 22:16:08 [INFO] 🔍 智能日志监控已启动
2025-07-28 22:16:08 [INFO] ============================================================
2025-07-28 22:16:08 [INFO] 📋 智能错误分析报告
2025-07-28 22:16:08,185 [INFO] ============================================================
2025-07-28 22:16:08,185 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 22:16:08,185 [INFO] ⚠️  严重程度: 中
2025-07-28 22:16:08,185 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 22:16:08,186 [INFO] 💡 解决方案:
2025-07-28 22:16:08,186 [INFO]    1. 以管理员身份运行程序
2025-07-28 22:16:08,186 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 22:16:08,187 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 22:16:08,187 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 22:16:08,187 [INFO] ============================================================
2025-07-28 22:16:08,187 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 22:16:08,187 [INFO] ============================================================
2025-07-28 22:16:08,188 [INFO] 📋 智能错误分析报告
2025-07-28 22:16:08,188 [INFO] ============================================================
2025-07-28 22:16:08,188 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 22:16:08,188 [INFO] ⚠️  严重程度: 低
2025-07-28 22:16:08,188 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 22:16:08,188 [INFO] 💡 解决方案:
2025-07-28 22:16:08,188 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 22:16:08,188 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 22:16:08,189 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 22:16:08,189 [INFO] ============================================================
2025-07-28 22:16:08,189 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 22:16:10,503 [INFO] === IndexTTS 系统检查 ===
2025-07-28 22:16:10,504 [INFO] Python版本: 3.10.16
2025-07-28 22:16:10,504 [INFO] PyTorch版本: 2.5.1+cu121
2025-07-28 22:16:10,504 [INFO] ✅ CUDA可用: 是
2025-07-28 22:16:10,504 [INFO] CUDA版本: 12.1
2025-07-28 22:16:10,504 [INFO] GPU数量: 1
2025-07-28 22:16:10,504 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 22:16:10,504 [INFO] 磁盘空间: 总计 631 GB, 已用 290 GB, 可用 341 GB
2025-07-28 22:16:10,504 [INFO] === 系统检查完成 ===
2025-07-28 22:16:10,506 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 22:16:23,060 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 22:16:23,060 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 22:16:23,060 [INFO] skip building fst for zh_normalizer ...
2025-07-28 22:16:23,560 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 22:16:23,561 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 22:16:23,561 [INFO] skip building fst for en_normalizer ...
2025-07-28 22:16:24,366 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-28 22:16:24,685 [INFO] 🔍 找到可用端口: 7866
2025-07-28 22:16:24,685 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 22:16:24,686 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-28 22:16:24,800 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 22:16:24,821 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-28 22:16:25,409 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-28 22:42:55 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 22:42:55 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 22:42:55 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 22:42:55 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 22:42:55 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 22:42:55 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 22:42:55 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 22:42:55 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 22:42:55 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 22:42:55 [INFO] 🔍 智能日志监控已启动
2025-07-28 22:42:55 [INFO] ============================================================
2025-07-28 22:42:55 [INFO] 📋 智能错误分析报告
2025-07-28 22:42:55,928 [INFO] ============================================================
2025-07-28 22:42:55,928 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 22:42:55,928 [INFO] ⚠️  严重程度: 中
2025-07-28 22:42:55,928 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 22:42:55,928 [INFO] 💡 解决方案:
2025-07-28 22:42:55,928 [INFO]    1. 以管理员身份运行程序
2025-07-28 22:42:55,928 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 22:42:55,928 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 22:42:55,929 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 22:42:55,929 [INFO] ============================================================
2025-07-28 22:42:55,929 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 22:42:55,930 [INFO] ============================================================
2025-07-28 22:42:55,930 [INFO] 📋 智能错误分析报告
2025-07-28 22:42:55,930 [INFO] ============================================================
2025-07-28 22:42:55,931 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 22:42:55,931 [INFO] ⚠️  严重程度: 低
2025-07-28 22:42:55,932 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 22:42:55,932 [INFO] 💡 解决方案:
2025-07-28 22:42:55,932 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 22:42:55,932 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 22:42:55,932 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 22:42:55,933 [INFO] ============================================================
2025-07-28 22:42:55,933 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 22:43:00,198 [INFO] === IndexTTS 系统检查 ===
2025-07-28 22:43:00,198 [INFO] Python版本: 3.10.16
2025-07-28 22:43:00,198 [INFO] PyTorch版本: 2.5.1+cu121
2025-07-28 22:43:00,198 [INFO] ✅ CUDA可用: 是
2025-07-28 22:43:00,198 [INFO] CUDA版本: 12.1
2025-07-28 22:43:00,198 [INFO] GPU数量: 1
2025-07-28 22:43:00,198 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 22:43:00,199 [INFO] 磁盘空间: 总计 631 GB, 已用 289 GB, 可用 341 GB
2025-07-28 22:43:00,199 [INFO] === 系统检查完成 ===
2025-07-28 22:43:00,203 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 22:43:17,277 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 22:43:17,277 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 22:43:17,278 [INFO] skip building fst for zh_normalizer ...
2025-07-28 22:43:17,728 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 22:43:17,728 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 22:43:17,728 [INFO] skip building fst for en_normalizer ...
2025-07-28 22:43:18,611 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-28 22:43:19,133 [INFO] 🔍 找到可用端口: 7866
2025-07-28 22:43:19,133 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 22:43:19,133 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-28 22:43:19,310 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 22:43:19,343 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-28 22:43:20,118 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-28 22:45:24 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 22:45:24 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 22:45:24 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 22:45:24 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 22:45:24 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 22:45:24 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 22:45:24 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 22:45:24 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 22:45:24 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 22:45:24 [INFO] 🔍 智能日志监控已启动
2025-07-28 22:45:24,366 [INFO] 📋 智能错误分析报告
2025-07-28 22:45:24,366 [INFO] ============================================================
2025-07-28 22:45:24,366 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 22:45:24,366 [INFO] ⚠️  严重程度: 中
2025-07-28 22:45:24,366 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 22:45:24,366 [INFO] 💡 解决方案:
2025-07-28 22:45:24,366 [INFO]    1. 以管理员身份运行程序
2025-07-28 22:45:24,366 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 22:45:24,366 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 22:45:24,366 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 22:45:24,366 [INFO] ============================================================
2025-07-28 22:45:24,366 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 22:45:24,366 [INFO] ============================================================
2025-07-28 22:45:24,366 [INFO] 📋 智能错误分析报告
2025-07-28 22:45:24,366 [INFO] ============================================================
2025-07-28 22:45:24,366 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 22:45:24,366 [INFO] ⚠️  严重程度: 低
2025-07-28 22:45:24,366 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 22:45:24,366 [INFO] 💡 解决方案:
2025-07-28 22:45:24,366 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 22:45:24,366 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 22:45:24,366 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 22:45:24,366 [INFO] ============================================================
2025-07-28 22:45:24,366 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 22:45:26,799 [INFO] === IndexTTS 系统检查 ===
2025-07-28 22:45:26,799 [INFO] Python版本: 3.10.16
2025-07-28 22:45:26,799 [INFO] PyTorch版本: 2.5.1+cu121
2025-07-28 22:45:26,799 [INFO] ✅ CUDA可用: 是
2025-07-28 22:45:26,799 [INFO] CUDA版本: 12.1
2025-07-28 22:45:26,799 [INFO] GPU数量: 1
2025-07-28 22:45:26,799 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 22:45:26,799 [INFO] 磁盘空间: 总计 631 GB, 已用 289 GB, 可用 341 GB
2025-07-28 22:45:26,799 [INFO] === 系统检查完成 ===
2025-07-28 22:45:26,799 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 22:45:38,007 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 22:45:38,007 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 22:45:38,007 [INFO] skip building fst for zh_normalizer ...
2025-07-28 22:45:38,356 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 22:45:38,356 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 22:45:38,356 [INFO] skip building fst for en_normalizer ...
2025-07-28 22:45:39,008 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-28 22:45:39,261 [INFO] 🔍 找到可用端口: 7866
2025-07-28 22:45:39,261 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 22:45:39,261 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-28 22:45:39,372 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 22:45:39,403 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-28 22:45:39,986 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
